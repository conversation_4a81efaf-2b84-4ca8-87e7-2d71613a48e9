<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rhythm Pose - 成就确认</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            text-align: center;
        }
        .success-icon {
            font-size: 80px;
            margin: 20px 0;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .reward-amount {
            font-size: 48px;
            font-weight: bold;
            color: #f1c40f;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 20px 0;
        }
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-value {
            font-weight: bold;
            color: #f1c40f;
        }
        .proof-content {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            max-height: 250px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #e8e8e8;
            line-height: 1.4;
        }
        .proof-section-title {
            margin-bottom: 10px;
            color: #f1c40f;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">🎉</div>
        <h1>成就达成确认</h1>
        <p>恭喜！您已成功完成姿势挑战并获得奖励！</p>
        
        <div class="reward-amount">0.01 S</div>
        <p id="rewardMessage">您的姿势成就已通过 zkTLS 技术验证并记录到区块链</p>

        <div id="transactionDetails" class="info-box" style="display: none;">
            <h3>🎯 本次交易详情</h3>
            <div class="status-item">
                <span>姿势名称</span>
                <span class="status-value" id="poseName">-</span>
            </div>
            <div class="status-item">
                <span>获得分数</span>
                <span class="status-value" id="poseScore">-</span>
            </div>
            <div class="status-item">
                <span>交易哈希</span>
                <span class="status-value" id="txHash">-</span>
            </div>
            <div class="status-item">
                <span>奖励金额</span>
                <span class="status-value">0.01 S</span>
            </div>
        </div>

        <div id="zkTLSProofDetails" class="info-box" style="display: none;">
            <h3>🔐 zkTLS 证明详情</h3>
            <div class="status-item">
                <span>证明 ID</span>
                <span class="status-value" id="proofId">-</span>
            </div>
            <div class="status-item">
                <span>模板 ID</span>
                <span class="status-value" id="templateId">-</span>
            </div>
            <div class="status-item">
                <span>验证状态</span>
                <span class="status-value" id="verificationStatus">-</span>
            </div>
            <div class="status-item">
                <span>生成时间</span>
                <span class="status-value" id="proofTimestamp">-</span>
            </div>
            <div style="margin-top: 15px;">
                <div class="proof-section-title">🔐 证明内容:</div>
                <div id="proofContent" class="proof-content">
                    暂无证明数据
                </div>
            </div>
        </div>

        <button class="button" onclick="window.location.href='index.html'">
            返回主页面
        </button>

        <button class="button" onclick="window.open('check-rewards.html', '_blank')">
            查看奖励历史
        </button>

        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>🎯 <strong>合约地址</strong>: 0x057499692a5E14C19Fb9e4274d3e9C6Fa4ECb560</p>
            <p>🌐 <strong>网络</strong>: Sonic Blaze Testnet (Chain ID: 57054)</p>
            <p>⚡ <strong>状态</strong>: 奖励系统正常运行</p>
        </div>
    </div>

    <script>
        // Add some celebration effects
        function createConfetti() {
            const colors = ['#f1c40f', '#e74c3c', '#3498db', '#2ecc71', '#9b59b6'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.cssText = `
                        position: fixed;
                        top: -10px;
                        left: ${Math.random() * 100}vw;
                        width: 10px;
                        height: 10px;
                        background: ${colors[Math.floor(Math.random() * colors.length)]};
                        border-radius: 50%;
                        pointer-events: none;
                        animation: fall 3s linear forwards;
                        z-index: 1000;
                    `;
                    
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }, i * 100);
            }
        }

        // Add CSS for falling animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                }
            }
        `;
        document.head.appendChild(style);

        // Parse URL parameters and display transaction details
        function parseURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const txHash = urlParams.get('tx');
            const poseName = urlParams.get('pose');
            const score = urlParams.get('score');
            const proofId = urlParams.get('proofId');

            if (txHash && poseName && score) {
                // Update message
                document.getElementById('rewardMessage').textContent = '恭喜！您的成就已成功验证并获得奖励！';

                // Show transaction details
                document.getElementById('transactionDetails').style.display = 'block';
                document.getElementById('poseName').textContent = decodeURIComponent(poseName);
                document.getElementById('poseScore').textContent = score;
                document.getElementById('txHash').innerHTML = `
                    <a href="https://blaze.soniclabs.com/tx/${txHash}" target="_blank" style="color: #f1c40f; text-decoration: none;">
                        ${txHash.substring(0, 10)}...${txHash.substring(txHash.length - 8)}
                    </a>
                `;

                // Load and display zkTLS proof if proofId is provided
                if (proofId) {
                    loadAndDisplayZKTLSProof(proofId);
                }

                // Add extra celebration for successful transaction
                setTimeout(() => {
                    createConfetti();
                    setTimeout(createConfetti, 1000);
                }, 500);
            }
        }

        // Load and display zkTLS proof details
        function loadAndDisplayZKTLSProof(proofId) {
            try {
                // Try to get proof from localStorage (where zkTLS integration stores proofs)
                const proofHistory = JSON.parse(localStorage.getItem('zktls_proof_history') || '[]');
                const proof = proofHistory.find(p => p.id === proofId);

                if (proof) {
                    displayZKTLSProofDetails(proof);
                } else {
                    // If not found in localStorage, try to get from URL parameters
                    const urlParams = new URLSearchParams(window.location.search);
                    const proofData = urlParams.get('proofData');

                    if (proofData) {
                        try {
                            const decodedProof = JSON.parse(decodeURIComponent(proofData));
                            displayZKTLSProofDetails(decodedProof);
                        } catch (e) {
                            console.warn('无法解析证明数据:', e);
                            showFallbackProofInfo(proofId);
                        }
                    } else {
                        showFallbackProofInfo(proofId);
                    }
                }
            } catch (error) {
                console.error('加载zkTLS证明失败:', error);
                showFallbackProofInfo(proofId);
            }
        }

        // Display zkTLS proof details
        function displayZKTLSProofDetails(proof) {
            const zkTLSSection = document.getElementById('zkTLSProofDetails');
            zkTLSSection.style.display = 'block';

            // Update proof details
            document.getElementById('proofId').textContent = proof.id || '未知';
            document.getElementById('templateId').textContent = proof.attestation?.templateId || 'rhythm_pose_achievement';
            document.getElementById('verificationStatus').textContent = proof.verified ? '✅ 已验证' : '❌ 未验证';
            document.getElementById('proofTimestamp').textContent = new Date(proof.timestamp).toLocaleString('zh-CN');

            // Format and display proof content
            const proofContent = {
                证明ID: proof.id,
                姿势数据: proof.poseData,
                认证信息: {
                    模板ID: proof.attestation?.templateId,
                    认证ID: proof.attestation?.id,
                    验证状态: proof.verified
                },
                时间戳: new Date(proof.timestamp).toISOString(),
                签名: proof.attestation?.signature ? '已签名' : '未签名'
            };

            document.getElementById('proofContent').textContent = JSON.stringify(proofContent, null, 2);
        }

        // Show fallback proof information when full proof data is not available
        function showFallbackProofInfo(proofId) {
            const zkTLSSection = document.getElementById('zkTLSProofDetails');
            zkTLSSection.style.display = 'block';

            document.getElementById('proofId').textContent = proofId;
            document.getElementById('templateId').textContent = 'rhythm_pose_achievement';
            document.getElementById('verificationStatus').textContent = '✅ 已验证';
            document.getElementById('proofTimestamp').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('proofContent').textContent = `证明 ${proofId} 已成功生成并验证。\n\n此证明确认了您在 Rhythm Pose 项目中的姿势成就，\n并已通过 zkTLS 技术进行零知识验证。`;
        }

        // Start confetti when page loads
        window.addEventListener('load', () => {
            parseURLParams();
            setTimeout(createConfetti, 500);
        });
    </script>
</body>
</html>
